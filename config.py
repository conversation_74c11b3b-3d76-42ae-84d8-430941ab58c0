# 三角洲地图扫描服务器性能配置文件
# 根据您的服务器性能调整这些参数

# ============ 性能配置 ============

# 最大并发线程数
# 低性能服务器: 5-10
# 中等性能服务器: 10-20
# 高性能服务器: 20-50
# 注意：442个服务器需要合理的并发数，但要避免超时
MAX_CONCURRENT_THREADS = 100

# 检测时间（秒）
# 低性能服务器: 4-6秒
# 中等性能服务器: 6-8秒
# 高性能服务器: 8-12秒
# 注意：这是每个服务器的连接时间，较短的时间可以避免超时
DETECTION_TIME = 6

# 自动检测间隔（秒）
# 建议至少300秒（5分钟），因为检测442个服务器需要较长时间
# 性能差的服务器可以设置为600秒（10分钟）或更长
AUTO_DETECTION_INTERVAL = 300

# 手动检测时的最大线程数
# 可以比自动检测稍微多一些，因为是手动触发
MANUAL_DETECTION_THREADS = 20

# ============ 其他配置 ============

# 服务器端口
SERVER_PORT = 4000

# 服务器主机
SERVER_HOST = '0.0.0.0'

# 是否启用调试模式
DEBUG_MODE = False

# ============ 配置说明 ============
"""
如果您遇到以下错误：
1. "can't start new thread" - 减少 MAX_CONCURRENT_THREADS 和 MANUAL_DETECTION_THREADS
2. 检测太慢 - 减少 DETECTION_TIME，但不要低于5秒
3. 服务器负载过高 - 增加 AUTO_DETECTION_INTERVAL，减少并发线程数
4. 内存不足 - 减少所有线程相关的参数

推荐配置组合（针对442个服务器）：
- 低性能服务器: MAX_CONCURRENT_THREADS=8, DETECTION_TIME=5, AUTO_DETECTION_INTERVAL=600
- 中等性能服务器: MAX_CONCURRENT_THREADS=15, DETECTION_TIME=6, AUTO_DETECTION_INTERVAL=300
- 高性能服务器: MAX_CONCURRENT_THREADS=25, DETECTION_TIME=8, AUTO_DETECTION_INTERVAL=180

时间估算：
- 8个并发：约需要 442/8 * 5秒 = 4.6分钟完成一次检测
- 15个并发：约需要 442/15 * 6秒 = 2.9分钟完成一次检测
- 25个并发：约需要 442/25 * 8秒 = 2.4分钟完成一次检测
"""
